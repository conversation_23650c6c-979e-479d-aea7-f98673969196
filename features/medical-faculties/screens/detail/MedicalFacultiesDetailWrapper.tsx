import { ActivityIndicator, View } from 'react-native'

import { useLocalSearchParams } from 'expo-router'
import { useEffect } from 'react'
import { HeaderBarDetail } from '../../components/common/HeaderBarDetail'
import { MedicalFacultiesDetailContent } from '../../components/detail/MedicalFacultiesDetailContent'
import { MedicalFacultiesRecord } from '../../components/quick-edit/MedicalFacultiesRecord'
import { useGetFacultyDetail } from '../../hooks/useGetFacultyDetail'
import { MedicalFacultiesContent } from '../../layouts/MedicalFacultiesContent'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'

export const MedicalFacultyDetailWrapper = () => {
  const { reset, isFinishInType, saveFacultyDetail, getStatusEditMode } = useMedicalFacultiesStore()

  const { id } = useLocalSearchParams()

  const { facultyDetail, isGetFacultyDetailLoading } = useGetFacultyDetail({
    id: id as string,
    params: {
      locale: 'all',
      select: {
        sampleSentences: true,
        name: true,
      },
    },
    useQueryOptions: {
      enabled: Boolean(id),
    },
  })

  useEffect(() => {
    if (facultyDetail) {
      saveFacultyDetail(facultyDetail)
    }
  }, [facultyDetail, saveFacultyDetail])

  useEffect(() => {
    return () => {
      reset()
    }
  }, [])

  if (isGetFacultyDetailLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" />
      </View>
    )
  }

  return (
    <View className="z-10 flex-1 bg-white ">
      <HeaderBarDetail />

      <MedicalFacultiesContent>
        {isFinishInType() && getStatusEditMode() ? (
          <MedicalFacultiesRecord />
        ) : (
          <MedicalFacultiesDetailContent />
        )}
      </MedicalFacultiesContent>
    </View>
  )
}
