import { Text } from '@/components/ui/Text/Text'
import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { KeywordSelectionLayout } from '@/features/medical-faculties/components/search/BottomSheetSelectKeyword'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'

import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import CheckIcon from '@/assets/icons/check-icon.svg'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { useRouter } from 'expo-router'
import { useEffect, useMemo, useState } from 'react'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'
import { ESymptomsType } from '../../types'
export const QuickEditFacultiesWrapper = () => {
  const { t } = useTranslation()
  const { openCustomSheet, closeSheet } = useSheetActions()

  const { getKeywords, updateSentence } = useMedicalFacultiesStore()

  const [selectedKeywords, setSelectedKeywords] = useState<Record<string, Keyword>>({})

  const { primaryLanguage } = useAppLanguage()

  const router = useRouter()

  useEffect(() => {
    setSelectedKeywords(getKeywords())
  }, [getKeywords])

  const keywords = useMemo(() => {
    return Object.values(getKeywords()).reduce((init, keyword: any) => {
      if (init?.[keyword.type]) {
        init[keyword.type].push(keyword)
      } else {
        init[keyword.type] = [keyword]
      }
      return init
    }, {} as any)
  }, [getKeywords])

  const handleApply = (keywordSelected: Record<string, Keyword>, type: ESymptomsType) => {
    setSelectedKeywords((prev) => {
      // Create a new object with all previous keywords
      const updated = { ...prev }

      // Remove all keywords of the current type
      Object.keys(updated).forEach((keyId) => {
        const keyword = updated[keyId] as any
        if (keyword.type === type) {
          delete updated[keyId]
        }
      })

      // Add the newly selected keywords with the type
      Object.entries(keywordSelected).forEach(([keyId, keyword]) => {
        updated[keyId] = {
          ...keyword,
          type,
        } as any
      })

      return updated
    })
    closeSheet()
  }

  const openSelectSymptom = (data: Keyword[], type: ESymptomsType) => {
    const initialKeywordSelected = data.reduce((init, keyword) => {
      init[keyword.id] = keyword
      return init
    }, {} as any)

    openCustomSheet({
      children: (
        <KeywordSelectionLayout
          keywordSelected={initialKeywordSelected}
          isShowCancelButton={true}
          onCancel={closeSheet}
          onApply={(keywordSelected) => {
            handleApply(keywordSelected, type)
          }}
        />
      ),
      baseProps: {
        snapPoints: ['70%', '70%'],
        enableDynamicSizing: false,
        enableOverDrag: false,
      },
    })
  }

  return (
    <View style={{ flex: 1, paddingHorizontal: 16 }}>
      <View className="flex-row items-center justify-between gap-2">
        <TouchableOpacity onPress={() => router.back()} className="p-2">
          <ArrowLeftIcon className="size-6" />
        </TouchableOpacity>

        <Text size="body3" variant="primary">
          {t('MES-1036')}
        </Text>

        <TouchableOpacity
          onPress={() => {
            updateSentence(selectedKeywords)
            router.back()
          }}
          className="p-2"
        >
          <CheckIcon className="size-6" />
        </TouchableOpacity>
      </View>

      {Object.entries(keywords).map(([type, data]: any) => {
        return (
          <View key={type} className="mt-4 gap-2">
            <Text size="body6" className="w-full">
              {t(renderTitle(type))}
            </Text>

            <TouchableOpacity
              onPress={() => openSelectSymptom(data, type)}
              className="w-full rounded-lg border border-[#DDDDEE] px-4 py-3"
            >
              <Text size="field2" className="w-full truncate">
                {data
                  .map(
                    (keyword: any) =>
                      (keyword.name as unknown as LocalizeField<string>)[
                        primaryLanguage as LocaleEnum
                      ],
                  )
                  .join(', ')}
              </Text>
            </TouchableOpacity>
          </View>
        )
      })}
    </View>
  )
}

const renderTitle = (type: string) => {
  switch (type) {
    case 'symptom':
      return 'MES-1037'
    case 'when':
      return 'MES-1038'
    case 'while':
      return 'MES-1039'
    case 'position':
      return 'MES-1040'
    default:
      return ''
  }
}
