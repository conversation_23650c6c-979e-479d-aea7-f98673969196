import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { medicalFacultyService } from '../services/faculties.service'
import { MedicalSentencePayload } from '../types'
import { medicalFacultiesQueryKeys } from './queryKeys'

type MedicalSentenceVariables = { payload: MedicalSentencePayload; facultyId: string }
export type MedicalSentenceResponse = {
  message: string
  result: {
    id: string
  }
}

export const useSaveMedicalSentence = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isSaveMedicalSentenceError,
    isPending: isSaveMedicalSentencePending,
    mutate: saveMedicalSentenceMutation,
    isSuccess: isSaveMedicalSentenceSuccess,
    ...rest
  } = useMutation({
    mutationKey: medicalFacultiesQueryKeys['save-medical-sentence'].base(),
    mutationFn: async ({
      payload,
      facultyId,
    }: MedicalSentenceVariables): Promise<MedicalSentenceResponse> => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const abortController = new AbortController()
      abortControllerRef.current = abortController

      return medicalFacultyService.saveMedicalSentence({
        facultyId,
        payload,
        options: {
          signal: abortControllerRef.current.signal,
        },
      })
    },
    ...options,
  })

  return {
    isSaveMedicalSentenceError,
    isSaveMedicalSentencePending,
    saveMedicalSentenceMutation,
    isSaveMedicalSentenceSuccess,
    ...rest,
  }
}
