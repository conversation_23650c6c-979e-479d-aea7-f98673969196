import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { medicalFacultyService } from '../services/faculties.service'
import { MedicalSentencePayload } from '../types'
import { medicalFacultiesQueryKeys } from './queryKeys'

type MedicalSentenceVariables = { payload: MedicalSentencePayload; medicalSentenceId: string }

export const useUpdateMedicalSentence = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isUpdateMedicalSentenceError,
    isPending: isUpdateMedicalSentencePending,
    mutate: updateMedicalSentenceMutation,
    isSuccess: isUpdateMedicalSentenceSuccess,
    ...rest
  } = useMutation({
    mutationKey: medicalFacultiesQueryKeys['update-medical-sentence'].base(),
    mutationFn: async ({ payload, medicalSentenceId }: MedicalSentenceVariables) => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const abortController = new AbortController()
      abortControllerRef.current = abortController

      return medicalFacultyService.updateMedicalSentence({
        medicalSentenceId,
        payload,
        options: {
          signal: abortControllerRef.current.signal,
        },
      })
    },
    ...options,
  })

  return {
    isUpdateMedicalSentenceError,
    isUpdateMedicalSentencePending,
    updateMedicalSentenceMutation,
    isUpdateMedicalSentenceSuccess,
    ...rest,
  }
}
