import { useTranslation } from 'react-i18next'
import Toast from 'react-native-toast-message'
import { useMedicalFacultiesStore } from '../stores/MedicalFacultiesStores'
import { MedicalSentencePayload } from '../types'
import { useSaveMedicalSentence } from './useSaveMedicalSentence'
import { useUpdateMedicalSentence } from './useUpdateMedicalSentence'

export const useMedicalSentenceStore = () => {
  const { idReport, saveIdRecord } = useMedicalFacultiesStore()

  const { t } = useTranslation()

  const { saveMedicalSentenceMutation } = useSaveMedicalSentence({
    options: {
      onSuccess: (data: any) => {
        const idRecord = data.result.id
        saveIdRecord(idRecord)
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: t('MES-904'),
          text2: t('MES-1048'),
        })
      },
    },
  })

  const { updateMedicalSentenceMutation } = useUpdateMedicalSentence({
    options: {
      onSuccess: () => {
        Toast.show({
          type: 'success',
          text1: t('MES-903'),
          text2: t('MES-1047'),
        })
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: t('MES-904'),
          text2: t('MES-1048'),
        })
      },
    },
  })

  const saveMedicalSentence = (payload: MedicalSentencePayload, facultyId: string) => {
    if (idReport) {
      updateMedicalSentenceMutation({ payload, medicalSentenceId: idReport })
    } else {
      saveMedicalSentenceMutation({ payload, facultyId })
    }
  }

  return {
    saveMedicalSentence,
  }
}
