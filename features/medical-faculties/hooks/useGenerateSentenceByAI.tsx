import { LocaleEnum } from '@/enums/locale.enum'
import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { medicalFacultyService } from '../services/faculties.service'
import { medicalFacultiesQueryKeys } from './queryKeys'

export type sentenceByAIPayload = {
  sentence: string
  user_language: LocaleEnum
}

export const useGenerateSentenceByAI = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isGenerateSentenceByAIError,
    isPending: isGenerateSentenceByAIPending,
    mutate: generateSentenceByAIMutation,
    ...rest
  } = useMutation({
    mutationKey: medicalFacultiesQueryKeys['generate-sentence-by-ai'].base(),
    mutationFn: async (payload: sentenceByAIPayload) => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const abortController = new AbortController()
      abortControllerRef.current = abortController

      return medicalFacultyService.generateSentenceByAI({
        payload,
        options: {
          signal: abortControllerRef.current.signal,
        },
      })
    },
    ...options,
  })

  return {
    isGenerateSentenceByAIError,
    isGenerateSentenceByAIPending,
    generateSentenceByAIMutation,
    ...rest,
  }
}
