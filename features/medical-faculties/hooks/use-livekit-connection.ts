import { useMemo } from 'react'
import { isExpoGo } from '@/utils/device'
import { CreateTokenArgs, useCreateTokenQuery } from './livekit.api'

export type TranslateConnectionOptions = CreateTokenArgs

export type TranslateConnectionDetails = {
  url: string
  token: string
  room_name?: string
  participant_name?: string
}

/**
 * Retrieves a LiveKit token for the translate feature from the FastAPI backend using TanStack Query.
 * This hook is specifically designed for the translate agent connection.
 */
export function useTranslateConnection(options?: TranslateConnectionOptions): {
  connectionDetails: TranslateConnectionDetails | undefined
  isLoading: boolean
  error: unknown
  refetch: () => void
} {
  // Disable query in Expo Go
  const { data, isLoading, error, refetch } = useCreateTokenQuery(options || {}, {
    enabled: !isExpoGo(),
  })

  const connectionDetails = useMemo(() => {
    if (!data || isExpoGo()) return undefined

    return {
      url: data.server_url,
      token: data.participant_token,
      room_name: data.room_name,
      participant_name: data.participant_name,
    }
  }, [data])

  return {
    connectionDetails,
    isLoading: isExpoGo() ? false : isLoading,
    error: isExpoGo() ? undefined : error,
    refetch: isExpoGo() ? () => {} : refetch,
  }
}
