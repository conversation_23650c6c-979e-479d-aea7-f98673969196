import { LocaleEnum } from '@/enums/locale.enum'

export enum ESymptomsType {
  BODY = 'body',
  WHEN = 'when',
  WHILE = 'while',
  POSITION = 'position',
  SYMPTOM = 'symptom',
}

export interface TextToSpeechPayload {
  text: string
  language: LocaleEnum
  includeMedia: boolean
  includeAudioFile: boolean
}

interface ISentenceResult {
  type: 'patient' | 'doctor'
  sentence: any
}

export interface AudioItem {
  language: LocaleEnum
  audio: string
  type: 'patient' | 'doctor'
}
export interface MedicalSentencePayload {
  sentenceResult: ISentenceResult[]
  sentenceAudio: AudioItem[]
}
