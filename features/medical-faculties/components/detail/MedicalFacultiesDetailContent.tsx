import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { isExpoGo } from '@/utils/device'
import { useFocusEffect } from 'expo-router'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import { Platform, TouchableOpacity, View } from 'react-native'
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useTranslateConnection } from '../../hooks/use-livekit-connection'
import { useGenManagement } from '../../hooks/useGenManagement'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'
import { DescriptionBox } from './DescriptionBox'
import { DescriptionBoxWithLiveKit } from './DescriptionBoxWithLiveKit'
import { HeaderDetail } from './HeaderDetail'
import { SuggestionTemplate } from './SuggestionTemplate'

import Toast from 'react-native-toast-message'

export const MedicalFacultiesDetailContent = () => {
  const { t } = useTranslation()
  const { user } = useAuthentication()
  const { primaryLanguage } = useAppLanguage()
  const [isAgentConnected, setIsAgentConnected] = useState(false)
  const [isMicrophoneEnabled, setIsMicrophoneEnabled] = useState(false)

  const timeCountDownRef = useRef(60)
  const [timeCountDown, setTimeCountDown] = useState(60) // Chỉ để trigger re-render UI
  const [valueDescription, setValueDescription] = useState<string>('')

  const { connectionDetails, refetch, error } = useTranslateConnection({
    agent_name: process.env.EXPO_PUBLIC_TRANSCRIBE_AGENT_NAME || 'transcribe-agent-dev',
    participant_name: user?.id || 'user-identity',
    participant_identity: user?.email || user?.id || 'user-identity',
    participant_metadata: { language: primaryLanguage },
  })

  const timerRef = useRef<number | undefined>(undefined)
  const intervalRef = useRef<number | undefined>(undefined)

  const { showLoading, type, getSentenceAudio } = useMedicalFacultiesStore()

  const { generateSentence } = useGenManagement()

  // Handle error
  useEffect(() => {
    if (error) {
      // Check if error has a message property, otherwise convert to string
      const errorMessage =
        error instanceof Error
          ? error.message
          : typeof error === 'object' && error !== null && 'data' in error
            ? String((error as any).data)
            : 'Unknown error'

      Toast.show({
        type: 'error',
        text1: `Error connecting to STT service: ${errorMessage}`,
      })
    }
  }, [error])

  // Refetch connection details when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refetch()
    }, [refetch]),
  )

  useEffect(() => {
    setValueDescription(getSentenceAudio(primaryLanguage as LocaleEnum))
    // console.log(getSentenceAudio(primaryLanguage as LocaleEnum))
  }, [getSentenceAudio, primaryLanguage])

  const handleContinue = () => {
    generateSentence({
      sentence: valueDescription,
      user_language: primaryLanguage as LocaleEnum,
    })
    showLoading()
  }
  const insets = useSafeAreaInsets()

  // Shared content structure
  const renderContent = (descriptionBox: React.ReactNode) => (
    <View className="flex-1">
      <KeyboardAwareScrollView
        bottomOffset={insets.bottom + 16}
        showsHorizontalScrollIndicator={false}
        keyboardShouldPersistTaps="never"
        keyboardDismissMode="on-drag"
        className="h-full"
        contentContainerStyle={{ paddingHorizontal: 16 }}
      >
        <HeaderDetail />

        {type === 'patient' ? (
          <Text size="body7" className="my-4">
            <Trans
              i18nKey="MES-998"
              components={{
                voice: (
                  <Text size="body10" variant="primary">
                    {t('MES-999')}
                  </Text>
                ),
              }}
            />{' '}
            :
          </Text>
        ) : (
          <Text size="body7" className="my-4" variant="error">
            <Trans
              i18nKey="MES-1044"
              components={{
                voice: (
                  <Text size="body10" variant="error">
                    {t('MES-999')}
                  </Text>
                ),
              }}
            />{' '}
            :
          </Text>
        )}

        {descriptionBox}

        <SuggestionTemplate />
      </KeyboardAwareScrollView>

      <View
        style={{
          paddingHorizontal: 16,
          boxShadow: '0px 4px 22px 0px #00000026',
          paddingBottom: Platform.OS === 'android' ? insets.bottom + 8 : 24,
        }}
      >
        <TouchableOpacity
          onPress={handleContinue}
          className="mt-4 w-full flex-row justify-center rounded-lg bg-primary-500 p-3"
        >
          <Text size="button3" variant="white">
            {t('MES-122')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  useEffect(() => {
    if (isAgentConnected && isMicrophoneEnabled) {
      // Reset timer về 60 khi mic thực sự được kết nối
      timeCountDownRef.current = 60
      setTimeCountDown(60)

      timerRef.current = setTimeout(() => {
        setIsAgentConnected(false)
        setIsMicrophoneEnabled(false)
        timeCountDownRef.current = 60
        setTimeCountDown(60)
      }, 60000)

      intervalRef.current = setInterval(() => {
        timeCountDownRef.current = timeCountDownRef.current - 1
        setTimeCountDown(timeCountDownRef.current)

        // Kiểm tra nếu hết thời gian
        if (timeCountDownRef.current <= 0) {
          setIsAgentConnected(false)
          setIsMicrophoneEnabled(false)
          timeCountDownRef.current = 60
          setTimeCountDown(60)
        }
      }, 1000)
    } else {
      // Clear timers khi mic không được kết nối
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isAgentConnected, isMicrophoneEnabled])

  const handleMicrophoneStateChange = (enabled: boolean) => {
    setIsMicrophoneEnabled(enabled)
  }

  const handleToggleConnection = () => {
    if (isAgentConnected) {
      if (timerRef.current) {
        clearTimeout(timerRef.current)
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      setIsAgentConnected(false)
      setIsMicrophoneEnabled(false)
      timeCountDownRef.current = 60
      setTimeCountDown(60)
    } else {
      setIsAgentConnected(true)
      // isMicrophoneEnabled sẽ được set khi mic thực sự kết nối
    }
  }

  // In Expo Go, render without LiveKit
  if (isExpoGo()) {
    return renderContent(
      <DescriptionBox
        valueDescription={valueDescription}
        onChangeValueDescription={setValueDescription}
        isAgentConnected={isAgentConnected}
        onToggleConnection={handleToggleConnection}
        timeCountDown={timeCountDown}
        onMicrophoneStateChange={handleMicrophoneStateChange}
        clearTranscript={() => handleMicrophoneStateChange(false)}
      />,
    )
  }

  // When NOT in Expo Go, wrap with LiveKitRoom and use wrapper component
  //eslint-disable-next-line @typescript-eslint/no-require-imports
  const { LiveKitRoom } = require('@livekit/react-native')
  return (
    <LiveKitRoom
      serverUrl={connectionDetails?.url}
      token={connectionDetails?.token}
      connect={isAgentConnected && !!connectionDetails?.token}
      audio={true}
      video={false}
    >
      {renderContent(
        <DescriptionBoxWithLiveKit
          valueDescription={valueDescription}
          onChangeValueDescription={setValueDescription}
          isAgentConnected={isAgentConnected}
          onToggleConnection={handleToggleConnection}
          timeCountDown={timeCountDown}
          onMicrophoneStateChange={handleMicrophoneStateChange}
        />,
      )}
    </LiveKitRoom>
  )
}
