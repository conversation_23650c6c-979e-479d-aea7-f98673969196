import { TextInput } from '@/components/ui/TextInput/TextInput'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, Keyboard, TouchableOpacity, View } from 'react-native'

import CloseIcon from '@/assets/icons/close-icon.svg'
import MicroPhoneIcon from '@/assets/icons/microphone-2.svg'
import MicroPhoneIconRed from '@/assets/icons/microphone-red.svg'

import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import Animated, { useAnimatedStyle, useSharedValue } from 'react-native-reanimated'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'
type DescriptionBoxProps = {
  isEditable?: boolean
  isAgentConnected: boolean
  valueDescription: string
  onChangeValueDescription: (value: string) => void
  onToggleConnection: () => void
  // Optional LiveKit props - only provided when wrapper is used
  fullTranscript?: string
  clearTranscript?: () => void
  localParticipant?: any
  isMicrophoneEnabled?: boolean
  timeCountDown?: number
  isAgentLoading?: boolean
  onMicrophoneStateChange?: (enabled: boolean) => void
}

const languageMap = {
  [LocaleEnum.JA]: 'MES-47',
  [LocaleEnum.VI]: 'MES-46',
}

export const DescriptionBox: React.FC<DescriptionBoxProps> = ({
  isEditable = true,
  isAgentConnected,
  valueDescription,
  onChangeValueDescription,
  onToggleConnection,
  fullTranscript,
  clearTranscript,
  localParticipant,
  isMicrophoneEnabled = false,
  isAgentLoading,
  timeCountDown,
  onMicrophoneStateChange,
}) => {
  const { t: tJa } = useTranslation(undefined, {
    lng: LocaleEnum.JA,
  })
  const { t: tVi } = useTranslation(undefined, {
    lng: LocaleEnum.VI,
  })
  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  const [isFocus, setIsFocus] = useState<boolean>(false)

  const pulseScale = useSharedValue(1)
  const pulseOpacity = useSharedValue(1)

  const animatedRingStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: pulseScale.value }],
      opacity: pulseOpacity.value,
    }
  })

  const { type } = useMedicalFacultiesStore()

  useEffect(() => {
    if (fullTranscript) {
      onChangeValueDescription(fullTranscript)
    }
  }, [fullTranscript, onChangeValueDescription])

  // Theo dõi trạng thái mic và thông báo cho parent component
  useEffect(() => {
    if (onMicrophoneStateChange) {
      onMicrophoneStateChange(isMicrophoneEnabled)
    }
  }, [isMicrophoneEnabled, onMicrophoneStateChange])

  const handleToggleConnection = async () => {
    Keyboard.dismiss()
    if (!isAgentConnected) {
      onToggleConnection()
    } else {
      const enabled = !isMicrophoneEnabled
      if (localParticipant) {
        await localParticipant.setMicrophoneEnabled(enabled)
      }
    }
  }

  const handleClearTranscript = () => {
    if (clearTranscript) {
      clearTranscript()
    }
    onChangeValueDescription('')
  }

  return (
    <View className="relative h-fit">
      {type === 'patient' ? (
        <TextInput
          value={valueDescription}
          onChangeText={onChangeValueDescription}
          multiline
          wrapperClassName={`p-0 border-transparent`}
          className="w-full rounded-lg border border-transparent bg-[#F8F8FC] px-3 py-2 text-base text-custom-text focus:border-primary-500 focus:bg-white"
          placeholder={`${tJa('MES-1030', { language: tJa(languageMap[primaryLanguage as LocaleEnum]) })}\n${tVi('MES-1030', { language: tVi(languageMap[primaryLanguage as LocaleEnum]) })}`}
          editable={isEditable}
          style={{ maxHeight: 265, minHeight: 265, paddingBottom: 50 }}
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
        ></TextInput>
      ) 
      : (
        <TextInput
          value={valueDescription}
          onChangeText={onChangeValueDescription}
          multiline
          wrapperClassName={`p-0 border-transparent`}
          className="w-full rounded-lg border border-transparent bg-[#F8F8FC] px-3 py-2 text-base text-custom-text focus:border-primary-500 focus:bg-white"
          placeholder={`${tJa('MES-1054', { language: tJa(languageMap[secondaryLanguage as LocaleEnum]) })}\n${tVi('MES-1054', { language: tVi(languageMap[secondaryLanguage as LocaleEnum]) })}`}
          editable={isEditable}
          style={{ maxHeight: 265, minHeight: 265, paddingBottom: 50 }}
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
        ></TextInput>
      )}

      <View className="absolute bottom-4 right-4 z-10 flex-row items-center gap-3">
        {valueDescription && (
          <TouchableOpacity onPress={handleClearTranscript}>
            <CloseIcon width={32} height={32} />
          </TouchableOpacity>
        )}
        <TouchableOpacity onPress={handleToggleConnection}>
          <Animated.View
            style={[animatedRingStyle]}
            className={`size-16 rounded-full ${isFocus ? 'bg-[#F8F8FC]' : 'bg-white '} items-center justify-center`}
          >
            {isAgentLoading ? (
              <ActivityIndicator size="small" color="#EF4444" />
            ) : !(isAgentConnected && isMicrophoneEnabled) ? (
              <MicroPhoneIcon width={24} height={24} />
            ) : (
              <MicroPhoneIconRed width={24} height={24} />
            )}
          </Animated.View>
        </TouchableOpacity>
      </View>

      {isAgentConnected && isMicrophoneEnabled && (
        <Text className="absolute bottom-0 right-2 z-10" size="body9" variant="subdued">
          {timeCountDown}s
        </Text>
      )}
    </View>
  )
}
