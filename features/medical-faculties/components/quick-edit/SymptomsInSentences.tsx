import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'
import { LocalizeField } from '@/types/global.type'
import { Link } from 'expo-router'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'

export const SymptomsInSentences = () => {
  const { t } = useTranslation()

  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  const { getKeywords } = useMedicalFacultiesStore()

  const keywords = useMemo(() => {
    return Object.values(getKeywords()) || []
  }, [getKeywords])

  return (
    <View className="mt-4 w-full gap-3">
      <Text size="body6">{t('MES-1034')}</Text>
      {keywords?.map((keyword, index) => (
        <Link
          key={index}
          href={{
            pathname:
              APP_ROUTES.MEDICAL_DICTIONARY?.children?.[
                AppRoutesEnum.MEDICAL_DICTIONARY_KEYWORD_DETAILS
              ]?.path,
            params: {
              id: keyword.id,
            },
          }}
          asChild
        >
          <TouchableOpacity>
            <View className="flex-row items-center justify-between gap-3 rounded-lg bg-[#F9F9FC] px-4 py-3">
              <Text size="body7" className="flex-1">
                {
                  (keyword.name as unknown as LocalizeField<string>)[primaryLanguage as LocaleEnum]
                }{' '}
              </Text>
              <Text size="body7" variant="subdued" className="flex-1">
                {
                  (keyword.name as unknown as LocalizeField<string>)[
                    secondaryLanguage as LocaleEnum
                  ]
                }{' '}
              </Text>
            </View>
          </TouchableOpacity>
        </Link>
      ))}
    </View>
  )
}
