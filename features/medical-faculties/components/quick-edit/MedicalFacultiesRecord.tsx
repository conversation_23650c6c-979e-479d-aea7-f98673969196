import { Text } from '@/components/ui/Text/Text'
import { useLocalSearchParams } from 'expo-router'
import { useTranslation } from 'react-i18next'
import { ScrollView, TouchableOpacity, View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useMedicalSentenceStore } from '../../hooks/useMedicalSentenceStore'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'
import { HeaderDetail } from '../detail/HeaderDetail'
import { BoxSentences } from './BoxSentences'
import { SymptomsInSentences } from './SymptomsInSentences'

export const MedicalFacultiesRecord = () => {
  const { t } = useTranslation()

  const { id } = useLocalSearchParams()

  const { type, setType, getPayloadMedicalSentence } = useMedicalFacultiesStore()

  const { saveMedicalSentence } = useMedicalSentenceStore()

  const insets = useSafeAreaInsets()

  const handleContinue = () => {
    const payload = getPayloadMedicalSentence()
    saveMedicalSentence(payload, id as string)
    if (type === 'doctor') return
    setType('doctor')
  }

  return (
    <View className="flex-1">
      <ScrollView
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}
      >
        <HeaderDetail />

        <Text className="mt-4" size="body9" variant="error">
          {t('MES-1033')}
        </Text>

        <BoxSentences />

        <SymptomsInSentences />
      </ScrollView>

      <View style={{ paddingBottom: insets.bottom + 16, paddingHorizontal: 16 }}>
        <TouchableOpacity
          onPress={handleContinue}
          className="mt-4 w-full flex-row justify-center rounded-lg bg-primary-500 p-3"
        >
          <Text size="button3" variant="white">
            {type === 'doctor' ? t('MES-500') : t('MES-1043')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
