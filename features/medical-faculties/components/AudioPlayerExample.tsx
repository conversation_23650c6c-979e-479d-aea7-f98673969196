import { Button } from '@/components/ui/Button/Button'
import { Text } from '@/components/ui/Text/Text'
import { TouchableOpacity, View } from 'react-native'
import { usePlayAudio } from '../hooks/usePlayAudio'

// Ví dụ URLs audio để test
const SAMPLE_AUDIO_URLS = [
  'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
  'https://www.soundjay.com/misc/sounds/fail-buzzer-02.wav',
  'https://file-examples.com/storage/fe68c1b7c1b58e2f89c4c95/2017/11/file_example_MP3_700KB.mp3',
]

export const AudioPlayerExample = () => {
  const {
    handlePlayAudio,
    pauseAudio,
    stopAudio,
    clearError,
    isLoading,
    error,
    isPlaying,
    currentAudioUrl,
  } = usePlayAudio()

  return (
    <View className="gap-4 p-4">
      <Text size="body3" className="text-center font-semibold">
        Audio Player Example
      </Text>

      {/* Status Display */}
      <View className="rounded-lg bg-gray-100 p-4">
        <Text size="body6" className="mb-2">
          <Text className="font-semibold">Trạng thái:</Text>{' '}
          {isLoading ? 'Đang tải...' : isPlaying ? 'Đang phát' : 'Dừng'}
        </Text>

        {currentAudioUrl && (
          <Text size="body7" className="mb-2">
            <Text className="font-semibold">URL hiện tại:</Text> {currentAudioUrl.substring(0, 50)}
            ...
          </Text>
        )}

        {error && (
          <View className="mt-2 rounded bg-red-100 p-2">
            <Text size="body7" className="text-red-600">
              <Text className="font-semibold">Lỗi:</Text> {error}
            </Text>
            <TouchableOpacity onPress={clearError} className="mt-2 rounded bg-red-500 px-3 py-1">
              <Text size="body8" className="text-center text-white">
                Xóa lỗi
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Sample Audio URLs */}
      <View className="gap-2">
        <Text size="body6" className="font-semibold">
          Chọn audio để phát:
        </Text>
        {SAMPLE_AUDIO_URLS.map((url, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => handlePlayAudio(url)}
            className={`rounded-lg border p-3 ${
              currentAudioUrl === url ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'
            }`}
            disabled={isLoading}
          >
            <Text size="body7">
              Audio {index + 1} {currentAudioUrl === url && '(Đang chọn)'}
            </Text>
            <Text size="body8" className="mt-1 text-gray-500">
              {url.substring(0, 40)}...
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Control Buttons */}
      <View className="flex-row gap-3">
        <Button className="flex-1" onPress={pauseAudio} disabled={!isPlaying || isLoading}>
          <Text>Tạm dừng</Text>
        </Button>

        <Button className="flex-1" onPress={stopAudio} disabled={!currentAudioUrl || isLoading}>
          <Text>Dừng</Text>
        </Button>
      </View>

      {/* Replay Current Audio */}
      {currentAudioUrl && (
        <Button
          onPress={() => handlePlayAudio(currentAudioUrl)}
          disabled={isLoading}
          className="bg-green-500"
        >
          <Text className="text-white">Phát lại audio hiện tại</Text>
        </Button>
      )}

      {/* Loading Indicator */}
      {isLoading && (
        <View className="rounded-lg bg-blue-100 p-3">
          <Text size="body6" className="text-center text-blue-600">
            🔄 Đang tải audio...
          </Text>
        </View>
      )}
    </View>
  )
}

// Hook usage example in a simple component
export const SimpleAudioButton = ({ audioUrl, label }: { audioUrl: string; label: string }) => {
  const { handlePlayAudio, isLoading, isPlaying, error } = usePlayAudio()

  return (
    <View className="gap-2">
      <TouchableOpacity
        onPress={() => handlePlayAudio(audioUrl)}
        disabled={isLoading}
        className={`rounded-lg p-3 ${
          isLoading ? 'bg-gray-300' : isPlaying ? 'bg-green-500' : 'bg-blue-500'
        }`}
      >
        <Text className="text-center text-white">
          {isLoading ? 'Đang tải...' : isPlaying ? '▶️ Đang phát' : `🔊 ${label}`}
        </Text>
      </TouchableOpacity>

      {error && (
        <Text size="body8" className="text-center text-red-500">
          {error}
        </Text>
      )}
    </View>
  )
}
