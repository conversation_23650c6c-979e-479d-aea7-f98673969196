import { View } from 'react-native'
import { LoadingHico } from '../components/common/LoadingHico'
import { useMedicalFacultiesStore } from '../stores/MedicalFacultiesStores'

type MedicalFacultiesContentProps = {
  children?: React.ReactNode
}
export const MedicalFacultiesContent = ({ children }: MedicalFacultiesContentProps) => {
  const { isLoading } = useMedicalFacultiesStore()

  // const inset = useSafeAreaInsets()
  return (
    <View className="relative w-full flex-1">
      <View className="flex h-full flex-col ">{children}</View>

      {isLoading && <LoadingHico />}
    </View>
  )
}
