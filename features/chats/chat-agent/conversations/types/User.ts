import { Account } from './Account'
import { AvailabilityStatus } from './common'

export type UserRole = 'administrator' | 'agent'

export type User = {
  id: number
  name: string
  account_id: number
  accounts: Account[]
  email: string
  pubsub_token: string
  avatar_url: string
  available_name: string
  role: UserRole
  identifier_hash: string
  availability: string
  thumbnail: string
  availability_status: AvailabilityStatus
  type: string
}
