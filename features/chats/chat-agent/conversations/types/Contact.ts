import { UnixTimestamp } from './Agent'
import { AvailabilityStatus } from './common'

export interface Contact {
  additionalAttributes: {
    location?: string
    companyName?: string
    city?: string
    country?: string
    description?: string
    createdAtIp?: string
    socialProfiles?: Record<string, string>
    twitterScreenName?: string
    telegramUsername?: string
  }
  availabilityStatus?: AvailabilityStatus
  createdAt: UnixTimestamp
  customAttributes: Record<string, string>
  email: string | null
  id: number
  identifier: string | null
  lastActivityAt: UnixTimestamp | null
  name: string | null
  phoneNumber: string | null
  thumbnail: string | null
  type: string
}
