import Constants, { ExecutionEnvironment } from 'expo-constants'
import * as Device from 'expo-device'
import { Dimensions } from 'react-native'

/**
 * Get device screen dimensions
 */
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window')
  return { width, height }
}

/**
 * Check if the device is a tablet based on screen size and device type
 */
export const isTablet = (): boolean => {
  const { width, height } = getScreenDimensions()
  const minDimension = Math.min(width, height)
  const maxDimension = Math.max(width, height)

  // Check by device type first (more reliable)
  if (Device.deviceType === Device.DeviceType.TABLET) {
    return true
  }

  // Fallback: check by screen dimensions
  // Tablet typically has min dimension >= 600px or aspect ratio > 1.3
  const aspectRatio = maxDimension / minDimension

  return minDimension >= 600 || aspectRatio > 1.3
}

/**
 * Get responsive tab bar height based on device type
 */
export const getTabBarHeight = (): number => {
  return isTablet() ? 72 : 56
}

/**
 * Get responsive spacing multiplier based on device type
 */
export const getSpacingMultiplier = (): number => {
  return isTablet() ? 1.2 : 1
}

/**
 * Check if the app is running in Expo Go
 */
export const isExpoGo = (): boolean => {
  return Constants.executionEnvironment === ExecutionEnvironment.StoreClient
}
