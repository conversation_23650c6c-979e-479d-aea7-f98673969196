import { MedicalFacultyWrapper } from '@/features/medical-faculties/screens/MedicalFacultiesWrapper'
import { withAuthentication } from '@/hoc/withAuthentication'

import { SafeAreaView } from 'react-native-safe-area-context'

function MedicalFacultyAppScreen() {
  return (
    <SafeAreaView className="bg-white" style={{ flex: 1 }} edges={['left', 'top', 'right']}>
      <MedicalFacultyWrapper />
    </SafeAreaView>
  )
}

export default withAuthentication(MedicalFacultyAppScreen)
